#!/usr/bin/env python3
"""Debug launcher for GAIA Service"""

import os
import sys
import subprocess

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set debug environment
os.environ.update({
    "DEBUG": "true",
    "LOG_LEVEL": "DEBUG",
    "HOST": "127.0.0.1",
    "PORT": "8000"
})

print("🐛 GAIA Service Debug Mode")
print("🌐 http://127.0.0.1:8000")
print("📚 http://127.0.0.1:8000/docs")

try:
    # Change to parent directory and run as module
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    subprocess.run([sys.executable, "-m", "gaia_service.main"])
except KeyboardInterrupt:
    print("\n🛑 Stopped")
