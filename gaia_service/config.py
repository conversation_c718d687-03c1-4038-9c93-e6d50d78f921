"""
GAIA Evaluation Service Configuration
"""

import os
from typing import Optional
from pathlib import Path
from dotenv import load_dotenv
from loguru import logger


class ServiceConfig:
    """Simple configuration for GAIA evaluation service."""

    def __init__(self):
        # Load .env file
        load_dotenv(override=True)

        # Service settings
        self.service_name = os.getenv("SERVICE_NAME", "GAIA Evaluation Service")
        self.debug = os.getenv("DEBUG", "false").lower() == "true"

        # Server settings
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))

        # Model configuration
        self.coordinator_model = os.getenv("GAIA_COORDINATOR_MODEL", "gpt-4o")
        self.task_model = os.getenv("GAIA_TASK_MODEL", "gpt-4o")
        self.answerer_model = os.getenv("GAIA_ANSWERER_MODEL", "gpt-4o")
        self.model_temperature = float(os.getenv("GAIA_TEMPERATURE", "0.0"))

        # Task processing settings
        self.max_concurrent_tasks = int(os.getenv("MAX_CONCURRENT_TASKS", "5"))
        self.default_timeout_seconds = int(os.getenv("DEFAULT_TIMEOUT_SECONDS", "1800"))
        self.max_retries = int(os.getenv("MAX_RETRIES", "3"))
        self.max_replanning_tries = int(os.getenv("MAX_REPLANNING_TRIES", "2"))

        # Storage settings
        self.temp_dir = os.getenv("TEMP_DIR", "tmp/gaia_service")
        self.data_dir = os.getenv("DATA_DIR", "data/gaia")

        # Create necessary directories
        Path(self.temp_dir).mkdir(parents=True, exist_ok=True)

        # Logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO")


def load_config() -> ServiceConfig:
    """Load configuration from environment variables and .env file."""
    config = ServiceConfig()
    logger.info(f"Configuration loaded: {config.service_name}")
    return config


# Global configuration instance
_config: Optional[ServiceConfig] = None


def get_config() -> ServiceConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = load_config()
    return _config
